import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  optimizeDeps: {
    exclude: ['lucide-react'],
    include: ['@arcgis/core']
  },
  define: {
    // Required for ArcGIS API for JavaScript
    global: 'globalThis',
  },
  css: {
    preprocessorOptions: {
      scss: {
        // Allow importing ArcGIS CSS
        additionalData: `@import "@arcgis/core/assets/esri/themes/light/main.css";`
      }
    }
  },
  server: {
    // Configure CORS for ArcGIS services
    proxy: {
      '/arcgis': {
        target: 'https://services.arcgis.com',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/arcgis/, '')
      }
    }
  }
});
