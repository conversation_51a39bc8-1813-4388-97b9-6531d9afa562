// ArcGIS CDN Module Loader
// This utility helps load ArcGIS modules from the CDN when using Vite

declare global {
  interface Window {
    require: any;
  }
}

export interface ArcGISModules {
  Map: any;
  MapView: any;
  SceneView: any;
  Basemap: any;
  FeatureLayer: any;
  GraphicsLayer: any;
  Graphic: any;
  Point: any;
  Polygon: any;
  Polyline: any;
  Circle: any;
  SimpleMarkerSymbol: any;
  PointSymbol3D: any;
  ObjectSymbol3DLayer: any;
  Search: any;
  Locate: any;
  BasemapToggle: any;
  LayerList: any;
  Measurement: any;
  DirectLineMeasurement3D: any;
  AreaMeasurement3D: any;
  Popup: any;
  geometryEngine: any;
  projectOperator: any;
  SpatialReference: any;
  SceneLayer: any;
  ElevationLayer: any;
  ImageryLayer: any;
  TileLayer: any;
  Layer: any;
  Locator: any;
  AddressCandidate: any;
  Geoprocessor: any;
  FeatureSet: any;
}

let arcgisModulesCache: ArcGISModules | null = null;

export async function loadArcGISModules(): Promise<ArcGISModules> {
  if (arcgisModulesCache) {
    return arcgisModulesCache;
  }

  return new Promise((resolve, reject) => {
    if (!window.require) {
      reject(new Error('ArcGIS API not loaded. Make sure the CDN script is included.'));
      return;
    }

    window.require([
      'esri/Map',
      'esri/views/MapView',
      'esri/views/SceneView',
      'esri/Basemap',
      'esri/layers/FeatureLayer',
      'esri/layers/GraphicsLayer',
      'esri/layers/ImageryLayer',
      'esri/layers/TileLayer',
      'esri/layers/SceneLayer',
      'esri/layers/ElevationLayer',
      'esri/layers/Layer',
      'esri/Graphic',
      'esri/geometry/Point',
      'esri/geometry/Polygon',
      'esri/geometry/Polyline',
      'esri/geometry/Circle',
      'esri/geometry/SpatialReference',
      'esri/geometry/geometryEngine',
      'esri/geometry/operators/projectOperator',
      'esri/symbols/SimpleMarkerSymbol',
      'esri/symbols/PointSymbol3D',
      'esri/symbols/ObjectSymbol3DLayer',
      'esri/widgets/Search',
      'esri/widgets/Locate',
      'esri/widgets/BasemapToggle',
      'esri/widgets/LayerList',
      'esri/widgets/Measurement',
      'esri/widgets/DirectLineMeasurement3D',
      'esri/widgets/AreaMeasurement3D',
      'esri/widgets/Popup',
      'esri/tasks/Locator',
      'esri/tasks/support/AddressCandidate',
      'esri/tasks/Geoprocessor',
      'esri/tasks/support/FeatureSet'
    ], (
      Map: any,
      MapView: any,
      SceneView: any,
      Basemap: any,
      FeatureLayer: any,
      GraphicsLayer: any,
      ImageryLayer: any,
      TileLayer: any,
      SceneLayer: any,
      ElevationLayer: any,
      Layer: any,
      Graphic: any,
      Point: any,
      Polygon: any,
      Polyline: any,
      Circle: any,
      SpatialReference: any,
      geometryEngine: any,
      projectOperator: any,
      SimpleMarkerSymbol: any,
      PointSymbol3D: any,
      ObjectSymbol3DLayer: any,
      Search: any,
      Locate: any,
      BasemapToggle: any,
      LayerList: any,
      Measurement: any,
      DirectLineMeasurement3D: any,
      AreaMeasurement3D: any,
      Popup: any,
      Locator: any,
      AddressCandidate: any,
      Geoprocessor: any,
      FeatureSet: any
    ) => {
      arcgisModulesCache = {
        Map,
        MapView,
        SceneView,
        Basemap,
        FeatureLayer,
        GraphicsLayer,
        ImageryLayer,
        TileLayer,
        SceneLayer,
        ElevationLayer,
        Layer,
        Graphic,
        Point,
        Polygon,
        Polyline,
        Circle,
        SpatialReference,
        geometryEngine,
        projectOperator,
        SimpleMarkerSymbol,
        PointSymbol3D,
        ObjectSymbol3DLayer,
        Search,
        Locate,
        BasemapToggle,
        LayerList,
        Measurement,
        DirectLineMeasurement3D,
        AreaMeasurement3D,
        Popup,
        Locator,
        AddressCandidate,
        Geoprocessor,
        FeatureSet
      };
      resolve(arcgisModulesCache);
    }, (error: any) => {
      reject(error);
    });
  });
}

// Helper function to check if ArcGIS API is available
export function isArcGISAvailable(): boolean {
  return typeof window !== 'undefined' && !!window.require;
}
