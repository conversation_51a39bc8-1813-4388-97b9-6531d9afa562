// ArcGIS CDN Module Loader
// This utility helps load ArcGIS modules from the CDN when using Vite

declare global {
  interface Window {
    require: any;
  }
}

export interface ArcGISModules {
  Map: any;
  MapView: any;
  SceneView: any;
  Basemap: any;
  FeatureLayer: any;
  GraphicsLayer: any;
  Graphic: any;
  Point: any;
  Polygon: any;
  Polyline: any;
  Circle: any;
  SimpleMarkerSymbol: any;
  PointSymbol3D: any;
  ObjectSymbol3DLayer: any;
  Search: any;
  Locate: any;
  BasemapToggle: any;
  LayerList: any;
  Measurement: any;
  DirectLineMeasurement3D: any;
  AreaMeasurement3D: any;
  Popup: any;
  geometryEngine: any;
  projectOperator: any;
  SpatialReference: any;
  SceneLayer: any;
  ElevationLayer: any;
  ImageryLayer: any;
  TileLayer: any;
  Layer: any;
}

let arcgisModulesCache: ArcGISModules | null = null;

export async function loadArcGISModules(): Promise<ArcGISModules> {
  if (arcgisModulesCache) {
    return arcgisModulesCache;
  }

  return new Promise((resolve, reject) => {
    if (!window.require) {
      reject(new Error('ArcGIS API not loaded. Make sure the CDN script is included.'));
      return;
    }

    // Load essential modules only (excluding problematic ones like Locator)
    const moduleList = [
      'esri/Map',
      'esri/views/MapView',
      'esri/views/SceneView',
      'esri/Basemap',
      'esri/layers/GraphicsLayer',
      'esri/Graphic',
      'esri/geometry/Point'
    ];

    window.require(moduleList, (...modules: any[]) => {
      const [EsriMap, MapView, SceneView, Basemap, GraphicsLayer, Graphic, Point] = modules;

      // Create a basic module cache with essential modules
      arcgisModulesCache = {
        Map: EsriMap,
        MapView,
        SceneView,
        Basemap,
        FeatureLayer: null, // Will be loaded on demand
        GraphicsLayer,
        ImageryLayer: null,
        TileLayer: null,
        SceneLayer: null,
        ElevationLayer: null,
        Layer: null,
        Graphic,
        Point,
        Polygon: null,
        Polyline: null,
        Circle: null,
        SpatialReference: null,
        geometryEngine: null,
        projectOperator: null,
        SimpleMarkerSymbol: null,
        PointSymbol3D: null,
        ObjectSymbol3DLayer: null,
        Search: null,
        Locate: null,
        BasemapToggle: null,
        LayerList: null,
        Measurement: null,
        DirectLineMeasurement3D: null,
        AreaMeasurement3D: null,
        Popup: null
      };

      // Load additional modules asynchronously
      loadAdditionalModules();

      resolve(arcgisModulesCache);
    }, (error: any) => {
      reject(new Error(`Failed to load core ArcGIS modules: ${error.message || error}`));
    });
  });
}

// Helper function to load additional modules after core modules are loaded
function loadAdditionalModules() {
  if (!window.require || !arcgisModulesCache) return;

  // Load symbols
  window.require(['esri/symbols/SimpleMarkerSymbol'], (SimpleMarkerSymbol: any) => {
    if (arcgisModulesCache) arcgisModulesCache.SimpleMarkerSymbol = SimpleMarkerSymbol;
  });

  // Load widgets
  window.require([
    'esri/widgets/Search',
    'esri/widgets/Locate',
    'esri/widgets/BasemapToggle',
    'esri/widgets/LayerList'
  ], (Search: any, Locate: any, BasemapToggle: any, LayerList: any) => {
    if (arcgisModulesCache) {
      arcgisModulesCache.Search = Search;
      arcgisModulesCache.Locate = Locate;
      arcgisModulesCache.BasemapToggle = BasemapToggle;
      arcgisModulesCache.LayerList = LayerList;
    }
  });

  // Load measurement widgets
  window.require([
    'esri/widgets/Measurement',
    'esri/widgets/DirectLineMeasurement3D',
    'esri/widgets/AreaMeasurement3D'
  ], (Measurement: any, DirectLineMeasurement3D: any, AreaMeasurement3D: any) => {
    if (arcgisModulesCache) {
      arcgisModulesCache.Measurement = Measurement;
      arcgisModulesCache.DirectLineMeasurement3D = DirectLineMeasurement3D;
      arcgisModulesCache.AreaMeasurement3D = AreaMeasurement3D;
    }
  });
}

// Helper function to check if ArcGIS API is available
export function isArcGISAvailable(): boolean {
  return typeof window !== 'undefined' && !!window.require;
}
